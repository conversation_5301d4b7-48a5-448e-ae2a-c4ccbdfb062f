from datetime import datetime
import threading
import json
import logging
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
import redis
import os

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger

# --- MemoryLayer Interface ---
class MemoryLayer(ABC):
    @abstractmethod
    def get(self, key: str) -> Any:
        pass

    @abstractmethod
    def set(self, key: str, value: Any):
        pass

# --- EphemeralMemory ---
class EphemeralMemory(MemoryLayer):
    """
    Stores transient data for a single pipeline step. Cleared after each pipeline.
    Thread-safe.
    """
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._lock = threading.Lock()
        self.logger = get_module_logger("EphemeralMemory")

    def get(self, key: str) -> Any:
        with self._lock:
            value = self._data.get(key)
            self.logger.debug(f"GET ephemeral[{key}] -> {value}")
            return value

    def set(self, key: str, value: Any):
        with self._lock:
            self._data[key] = value
            self.logger.debug(f"SET ephemeral[{key}] = {value}")

    def clear(self):
        with self._lock:
            self.logger.debug("CLEAR ephemeral memory")
            self._data.clear()

    def get_all(self) -> Dict[str, Any]:
        with self._lock:
            return dict(self._data)

# --- ContextualMemory ---
class ContextualMemory(MemoryLayer):
    """
    Stores session-specific data (chat history, intents, slots, etc.). Cleared after session ends.
    Thread-safe.
    """
    def __init__(self, session_id: str):
        self.session_id = session_id
        self._data: Dict[str, Any] = {}
        self._lock = threading.Lock()
        self.logger = logging.getLogger(f"ContextualMemory[{session_id}]")

    def get(self, key: str) -> Any:
        with self._lock:
            value = self._data.get(key)
            self.logger.debug(f"GET contextual[{key}] -> {value}")
            return value

    def set(self, key: str, value: Any):
        with self._lock:
            self._data[key] = value
            self.logger.debug(f"SET contextual[{key}] = {value}")

    def clear(self):
        with self._lock:
            self.logger.debug("CLEAR contextual memory")
            self._data.clear()

    def get_all(self) -> Dict[str, Any]:
        with self._lock:
            return dict(self._data)

    def save_to_file(self, filepath: str):
        """
        Save the current contextual memory (session data) to a file as JSON Line (JSONL).
        Appends to the file, so it accumulates logs from multiple sessions/runs.
        """
        with self._lock:
            # Add a timestamp and session_id for clearer logging of multiple sessions
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "session_id": self.session_id,
                "data": self._data
            }
            with open(filepath, 'a', encoding='utf-8') as f:
                json.dump(log_entry, f, ensure_ascii=False, indent=2)
                f.write('\n') # Add a newline to make it JSONL format
        self.logger.info(f"Contextual memory (session log) appended to {filepath}")

# --- PersistentMemory ---
class PersistentMemory(MemoryLayer):
    """
    Stores long-term data in Redis. Keys are prefixed by user_id or session_id.
    Serializes complex objects to JSON.
    """
    def __init__(self):
        # load_dotenv() # Removed - now in __main__.py
        redis_host = os.getenv("REDIS_HOST", "localhost")
        redis_port = int(os.getenv("REDIS_PORT", 6379))
        self.redis = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.logger = logging.getLogger("PersistentMemory")

    def _make_key(self, prefix: str, key: str) -> str:
        return f"{prefix}:{key}"

    def get(self, key: str) -> Any:
        try:
            value = self.redis.get(key)
            if value is None:
                self.logger.debug(f"GET persistent[{key}] -> None")
                return None
            try:
                # Try to decode JSON
                obj = json.loads(value)
                self.logger.debug(f"GET persistent[{key}] -> [JSON] {obj}")
                return obj
            except Exception:
                self.logger.debug(f"GET persistent[{key}] -> [RAW] {value}")
                return value
        except Exception as e:
            self.logger.error(f"Error getting persistent[{key}]: {e}")
            return None

    def set(self, key: str, value: Any):
        """
        Set a value in persistent memory.
        - If value is a dict and existing value is a dict, merge (update) them.
        - If value is a list and existing value is a list, append new items.
        - Otherwise, overwrite as before.
        """
        try:
            existing = self.get(key)
            # Merge dicts
            if isinstance(value, dict) and isinstance(existing, dict):
                merged = existing.copy()
                merged.update(value)
                value = merged
            # Append to lists
            elif isinstance(value, list) and isinstance(existing, list):
                value = existing + value
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            else:
                value = str(value)
            self.redis.set(key, value)
            self.logger.debug(f"SET persistent[{key}] = {value}")
        except Exception as e:
            self.logger.error(f"Error setting persistent[{key}]: {e}")

    def get_all(self, prefix: str) -> Dict[str, Any]:
        try:
            pattern = f"{prefix}:*"
            keys = self.redis.keys(pattern)
            result = {}
            for key in keys:
                result[key] = self.get(key)
            self.logger.debug(f"GET_ALL persistent[{prefix}] -> {result}")
            return result
        except Exception as e:
            self.logger.error(f"Error getting all persistent[{prefix}]: {e}")
            return {}

# --- MemoryManager ---
class MemoryManager:
    """
    Orchestrates Ephemeral, Contextual, and Persistent memory layers.
    Provides unified API for get/set/clear and explicit memory saving.
    """
    def __init__(self, session_id: str, user_id: Optional[str] = None):
        self.session_id = session_id
        self.user_id = user_id or f"anon_{session_id}"
        self.ephemeral = EphemeralMemory()
        self.contextual = ContextualMemory(session_id)
        self.persistent = PersistentMemory()
        self.logger = logging.getLogger(f"MemoryManager[{self.session_id}]")

    def get(self, key: str) -> Any:
        # Ephemeral -> Contextual -> Persistent
        value = self.ephemeral.get(key)
        if value is not None:
            self.logger.debug(f"GET {key} from ephemeral -> {value}")
            return value
        value = self.contextual.get(key)
        if value is not None:
            self.logger.debug(f"GET {key} from contextual -> {value}")
            return value
        persistent_key = f"{self.user_id}:{key}"
        value = self.persistent.get(persistent_key)
        if value is not None:
            self.logger.debug(f"GET {key} from persistent -> {value}")
        else:
            self.logger.debug(f"GET {key} -> None (not found)")
        return value

    def set(self, layer: str, key: str, value: Any):
        if layer == "ephemeral":
            self.ephemeral.set(key, value)
        elif layer == "contextual":
            self.contextual.set(key, value)
        elif layer == "persistent":
            persistent_key = f"{self.user_id}:{key}"
            self.persistent.set(persistent_key, value)
        else:
            self.logger.error(f"Unknown memory layer: {layer}")
            raise ValueError(f"Unknown memory layer: {layer}")
        self.logger.info(f"SET {layer}[{key}] = {value}")

    def clear_ephemeral(self):
        self.logger.info("Clearing ephemeral memory")
        self.ephemeral.clear()

    def clear_contextual(self):
        self.logger.info("Clearing contextual memory")
        self.contextual.clear()

    def get_all_contextual(self) -> Dict[str, Any]:
        return self.contextual.get_all()

    def get_all_persistent(self) -> Dict[str, Any]:
        return self.persistent.get_all(self.user_id)

    def add_conversation_turn(self, user_message: str, ai_message: str, intent: Optional[str] = None):
        """
        Adds a user message and an AI response as a turn to the contextual conversation history.
        Automatically includes timestamp, user_id, and session_id.
        Optionally stores the detected intent.
        """
        if intent:
            self.contextual.set("intent", intent)
            self.logger.debug(f"Stored intent '{intent}' in contextual memory.")

        conversation = self.contextual.get("conversation") or []

        # Add user turn
        user_turn = {
            "role": "user",
            "text": user_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        conversation.append(user_turn)

        # Add AI turn
        ai_turn = {
            "role": "ai",
            "text": ai_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        conversation.append(ai_turn)

        self.contextual.set("conversation", conversation)
        self.logger.debug(f"Added conversation turn for session {self.session_id}")

    def explicit_save(self, intent: str, slots: Dict[str, Any]):
        """
        Detects 'Remember this...' or explicit save intents and stores in persistent memory.
        Example: intent='save_preference', slots={'preference': 'language', 'value': 'en'}
        """
        if intent == "save_preference" and "preference" in slots and "value" in slots:
            key = slots["preference"]
            value = slots["value"]
            persistent_key = f"{self.user_id}:{key}"
            self.persistent.set(persistent_key, value)
            self.logger.info(f"Explicitly saved {persistent_key} = {value}")
            return True
        self.logger.warning(f"Explicit save intent not recognized or missing slots: {intent}, {slots}")
        return False

    # TODO: Add support for advanced memory analytics and summarization in future versions.
    # def save_session_summary(self, llm_extract_func):
    #     """
    #     Extracts relevant information from contextual memory, sends it to an LLM for summarization/extraction,
    #     and saves the returned structured data to persistent memory. Call this before clearing contextual memory.
    #     Args:
    #         llm_extract_func: a function that takes session data (dict) and returns a dict of persistent info
    #     Returns:
    #         The dict of persistent info saved, for logging/debugging.
    #     """
    #     # 1. Extract contextual/session data
    #     session_data = self.get_all_contextual()
    #     self.logger.info(f"Extracting persistent info from session data for user {self.user_id}")

    #     # 2. Send to LLM for extraction 
    #     persistent_info = llm_extract_func(session_data)
    #     if not isinstance(persistent_info, dict):
    #         self.logger.error("LLM extraction did not return a dict. Nothing saved.")
    #         return None

    #     # 3. Save to persistent memory
    #     for key, value in persistent_info.items():
    #         self.set("persistent", key, value)
    #         self.logger.info(f"Saved to persistent memory: {key} = {value}")

    #     return persistent_info
    
    # def llm_extract_func(session_data):
    #     # Call your LLM here and return a dict of info to persist
    #     # For now, just a placeholder:
    #     return {"language": "en", "favorite_color": "blue"}
import json
import sys
from pathlib import Path
from typing import Optional
from pydantic import ValidationError

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from .states.Workflow import WorkflowWrapper
from core.exceptions import SchemaError, WorkflowError, StateTransitionError
from core.logger_config import get_module_logger

logger = get_module_logger("workflow_schema_loader")

class WorkflowSchemaLoader:
    @staticmethod
    def load(file_path: str) -> Optional[WorkflowWrapper]:
        """
        Loads a workflow schema JSON file and parses it into a WorkflowWrapper object.

        Args:
            file_path (str): Path to the workflow JSON file.

        Returns:
            WorkflowWrapper: Parsed workflow object, or None if there's an error.
            
        Raises:
            SchemaError: If the JSON schema is invalid
            WorkflowError: If there are logical errors in the workflow definition
        """
        logger.debug(
            "Loading workflow file",
            action="load_workflow",
            input_data={"file_path": file_path},
            layer="schema_loading"
        )
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                # Use model_validate instead of constructor for validation
                workflow = WorkflowWrapper.model_validate(data)
                WorkflowSchemaLoader.validate_workflow(workflow)
                logger.info(
                    "Successfully loaded workflow schema",
                    action="load_workflow",
                    output_data={"file": file_path, "workflow_id": workflow.workflow.id},
                    layer="schema_loading"
                )
                return workflow
        except FileNotFoundError:
            logger.error(
                "Workflow file not found",
                action="load_workflow",
                input_data={"file": file_path},
                reason="File not found",
                layer="schema_loading"
            )
            raise SchemaError(f"Workflow schema file not found: {file_path}")
        except json.JSONDecodeError as e:
            logger.error(
                "Invalid JSON format in workflow file",
                action="load_workflow",
                input_data={"file": file_path},
                reason=str(e),
                layer="schema_loading"
            )
            raise SchemaError(f"Invalid JSON format in workflow schema: {e}")
        except ValidationError as e:
            logger.error(
                "Workflow schema validation failed",
                action="load_workflow",
                input_data={"file": file_path},
                reason=str(e),
                layer="schema_loading"
            )
            raise SchemaError(f"Workflow schema validation failed: {e}")
    
    def validate_workflow(workflow: WorkflowWrapper) -> None:
        """
        Validates the workflow object to ensure it meets all requirements.

        Args:
            workflow (WorkflowWrapper): The workflow object to validate.

        Raises:
            ValidationError: If the workflow does not meet validation requirements.
            ValueError: If there are logical errors in the workflow structure.
        """
        try:
            # Track state transitions to detect cycles
            state_transitions = {}
            
            # Validate states and transitions
            for state_id, state in workflow.workflow.states.items():
                # Check for duplicate output keys across pipeline steps
                defined_outputs = {}
                for output in state.expected_output:
                    if output in defined_outputs:
                        raise ValueError(f"Duplicate output key '{output}' found in state '{state_id}'.")
                    defined_outputs[output] = state_id
                
                # Track state transitions for cycle detection
                if hasattr(state, 'transitions'):
                    state_transitions[state_id] = [t.target for t in state.transitions]
            
            # Detect cycles in state transitions
            WorkflowSchemaLoader._detect_cycles(state_transitions)
            
        except ValidationError as e:
            print(f"❌ Error: Workflow validation failed:\n{e}")
            raise e
        except ValueError as e:
            print(f"❌ Error: Workflow validation failed: {str(e)}")
            raise ValueError(f"Workflow validation failed: {str(e)}")

    @staticmethod
    def _detect_cycles(transitions):
        """
        Detects cycles in state transitions using DFS.
        
        Args:
            transitions: Dictionary mapping state IDs to lists of target state IDs.
            
        Raises:
            StateTransitionError: If a cycle is detected in the state transitions.
        """
        visited = set()
        path = set()
        
        def dfs(state):
            if state in path:
                cycle_path = " -> ".join(list(path) + [state])
                logger.error(
                    "Cycle detected in state transitions",
                    action="validate_workflow",
                    output_data={"cycle": cycle_path},
                    reason="Circular dependency in state transitions",
                    layer="schema_validation"
                )
                raise StateTransitionError(f"Cycle detected in state transitions: {cycle_path}")
            
            if state in visited or state not in transitions:
                return
            
            visited.add(state)
            path.add(state)
            
            for target in transitions[state]:
                dfs(target)
            
            path.remove(state)
        
        for state in transitions:
            if state not in visited:
                dfs(state)


        
